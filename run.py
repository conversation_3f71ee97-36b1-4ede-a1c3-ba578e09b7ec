import requests
import logging
from DrissionPage import ChromiumPage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('spider.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class Spider:
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.cookies_file = "cookies.json"
        self.base_url = "https://www.glassdoor.com.hk"

        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'apollographql-client-name': 'community',
            'apollographql-client-version': '4.0.2',
            'content-type': 'application/json',
            'gd-csrf-token': 'zTKosX5gZKdeQPLaKxG4dg:WhSZtcgkhacBZxqWoBDlka6hWscBQH7LVm1C3hWkp0dxhYq80Fw3YB_5JLyln130Mp-rfGpUJ4_uObhVokYpXA:_sVhU2eKyGulImys_EUzFSTWjt_ivYaw0EXTReT3-vU',
            'origin': 'https://www.glassdoor.com.hk',
            'priority': 'u=1, i',
            'referer': 'https://www.glassdoor.com.hk/',
            'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
            'sec-ch-ua-arch': '"x86"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-full-version': '"139.0.7258.127"',
            'sec-ch-ua-full-version-list': '"Not;A=Brand";v="99.0.0.0", "Google Chrome";v="139.0.7258.127", "Chromium";v="139.0.7258.127"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': '"Windows"',
            'sec-ch-ua-platform-version': '"10.0.0"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
        }
        self.cookies = {
            'rttdf': 'true',
            'gdId': 'eb3f8a72-21e8-401e-ac79-46f336daf90d',
            'rl_page_init_referrer': 'RudderEncrypt%3AU2FsdGVkX1%2BX3ijb5BYjmTG39OowijpVaTx%2FLaBJY%2FNhKyqtXicTPvkqNo%2FFaxnc',
            'rl_page_init_referring_domain': 'RudderEncrypt%3AU2FsdGVkX1%2BPX1zrbKr3ZJaRbsa9TV3RkPYj9bm40%2F772H%2BDLV9P%2B7RbAciZ1DGb',
            '_optionalConsent': 'true',
            'indeedCtk': '1j2e9icsvi9uh801',
            '_ga': 'GA1.3.1261032771.1755138768',
            'uc': '3A09F7A1DD87325AFC2033B1FAF7594933F44001CB0C77D55AFABA635A5BD629DCAE660674CF0790BEECC2B2EA4E7C3CF8777F17E0F756D9397F5CAEB061BA68EEDB622CB15FAF98874466976FFD6E30088D5CFC49ABFE3625EF572701A41C337BA009648A81C8EBFA62DA649EAA085F549AFFD9AB9C783B483D6D89250F11B79071B19A82EA482270A6DF2C2D7347B5',
            'muxData': '=undefined&mux_viewer_id=6dac4bdc-6a15-4018-90ad-21ddad661a99&msn=0.3687204790453158&sid=08e61c19-1bf9-43c5-a011-3355bbedd2e0&sst=1755499582176&sex=1755501130920',
            '_gid': 'GA1.3.1632636146.1755503565',
            'gdsid': '1755574589656:1755593005412:2CBD01148CEB8D99919E8D5327ED85EA',
            'asst': '1755593005.0',
            'at': 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************.T2OIebPphF1bKJbc1QWeSubTDMAMkyctQMqhZMpT8ZtBo4QKmrfSb7ANwE04-u7Dbbba07MYNBjQHnf3JEd5iZaCYm_VExQEUteZ22Lh21FtYTzX8FFaXiBlBQuR8j4q5XYTvGwUo5KpbGMw_-N-V9vwPrD5obm94ydz5DaOhoeAyeOlGKtskUFhVk4psXpjFCzBqpIjMMIVIlZWSqraCcOeIMHLhnY7Jmxy8WP0cM16RpSwqdGSsP5VuuCvnhNF8MddzoFvnZ7wm3y9fwhAzfWqWw5X_Mc_c__50lpfWhpy_pthJbO73vTP0ulk_rz1dv2VdY5c2-Kmqc9lwJWDxQ',
            '__cf_bm': 'w8w9h6wkhCYeA70szezCvXt8oSvWhhgSRE7n_fsX0Ik-1755593006-1.0.1.1-ieNBQIo.QV9R.wIRyhYlaAj.Ys_KkYzAWnDZBOeWRexKCTGT96_x5AnhmBZGiHsQ1_R0.ROjxJQ0RcjaH0u_sA0zEXphOToiJiSmzrxI6dU',
            '_cfuvid': 'dk1zMuReZ.t9A7F4a0lSCeOAAqegLH4ABMUFLkpe_xk-1755593006013-0.0.1.1-604800000',
            'cf_clearance': 'iYQo9PqEPGLfRgrRzFxBA5x_4pFRV_PFnamL8DS64ww-1755593012-1.2.1.1-iKrZpIzsELp72m4HUhQ84Z4XYeOWIVCgbmMTYPiE9GpN161srn8dcId6iWF1SAokuWpgMgW71r9dsMcHzVexoILNGTsaqGMXzfquELz7qAyG_8l1BNM2nsPhK_L_HY_ADQfDDaMTgp6gkgDS0vfGYMKSVvHrZOy96LmYO7lltbKQ9Yu1AkzBj3Z1H5FkN68YLFmTCMjuSu4tDeMtKZkm8O1SJQHyI4h_UHGKEoxEwFo',
            'AWSALB': 'mZzyOrGkk/fkE7f+siTAiv8hxJ5XNq1NsKyu5E3BwGLVH89GEWOvrKH068jCgen4PBibJgYteE6Zsyo0EVEBoGPXD4w+QknG5UR19KRBJy3IVjhCGIyg21Jcl80y',
            'AWSALBCORS': 'mZzyOrGkk/fkE7f+siTAiv8hxJ5XNq1NsKyu5E3BwGLVH89GEWOvrKH068jCgen4PBibJgYteE6Zsyo0EVEBoGPXD4w+QknG5UR19KRBJy3IVjhCGIyg21Jcl80y',
            'JSESSIONID': 'EDCF5B649B9C5B52BE1162179CDA7DE0',
            'GSESSIONID': 'EDCF5B649B9C5B52BE1162179CDA7DE0',
            'cass': '0',
            'rsSessionId': '1755593011287',
            'bs': 'cx1y6Mhkthz6ilFO9FcAaQ:0A7SWs6DYM_MfjZ6sC0sVHHFMzDZ5Y6tnoKxxf7DAEIgh9MFqYMeliMk3lwwkfyCcMXoZkYV4SG4Em9btBMMbTDd3W_bEiGMN_wEXpZX-xM:ejRWmv48VabbQURne4-sNQIl76RM0XQal6cprJ6PxHU',
            'rl_user_id': 'RudderEncrypt%3AU2FsdGVkX19MKX6fiNSA%2BL7DS84R5bZtjNg%2FkE2PLDs%3D',
            'rl_trait': 'RudderEncrypt%3AU2FsdGVkX1%2Buf7OgBn059AV3y76eO1Nvrrs8bQ4GcaEBSxro5Vy5%2FYPbmY7sLyI8mHSyB9fWbabpuB5rl8s0mhBtTc%2B034WrMSt7SbKcfjE0oSf4jD34e2YFK2qq0g0P2vmETk1A7djvwdskHkfJk36yUPudv2ZMLG2%2FiCPXwxE%3D',
            'rl_group_id': 'RudderEncrypt%3AU2FsdGVkX18yoGK1QPbKZZjwQbaBpiF8IuQT4zgPllo%3D',
            'rl_group_trait': 'RudderEncrypt%3AU2FsdGVkX18GGnRY3eQ4rwZh0sG6hGLipOaJ1GVg%2F54%3D',
            'rl_anonymous_id': 'RudderEncrypt%3AU2FsdGVkX1%2BkwQoLvXJg%2BB5nHkOXbnTRGvcw35yHZJku6kO2ZepDg4ser%2Fs1Ni21ODOR3NhBcGaNKeSQqzGckA%3D%3D',
            'rsReferrerData': '%7B%22currentPageRollup%22%3A%22%2Fcommunity%2Findex%22%2C%22previousPageRollup%22%3A%22%2Fsurveys%2Fbenefits%2Fcreate%22%2C%22currentPageAbstract%22%3A%22%2FCommunity%2Findex.htm%22%2C%22previousPageAbstract%22%3A%22%2Fsurveys%2Fbenefits%2Fcreate%22%2C%22currentPageFull%22%3A%22https%3A%2F%2Fwww.glassdoor.com.hk%2FCommunity%2Findex.htm%22%2C%22previousPageFull%22%3A%22https%3A%2F%2Fwww.glassdoor.com.hk%2Fsurveys%2Fbenefits%2Fcreate%3Fc%3DGIVETOGET_CONTENT_WALL_HARDSELL%26i%3D2705%26ji%3D627%26jt%3DComputer%2BProgrammer%26locId%3D2308631%22%7D',
            'OptanonConsent': 'isGpcEnabled=0&datestamp=Tue+Aug+19+2025+16%3A44%3A33+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)&version=202407.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=6a750cfa-df66-47ac-905e-228cddcd06e9&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0003%3A1%2CC0002%3A1%2CC0004%3A1%2CC0017%3A1&AwaitingReconsent=false',
            'cdArr': '77',
            '_dd_s': 'rum=0&expire=1755593988303',
            'rl_session': 'RudderEncrypt%3AU2FsdGVkX1%2FW3oVXRbLTElbCflnDHCPBCgDRJm47RrSsOq5FRkyfJkVF6BXhyYIRa6RR1OYZekUydexOIQacHC%2FcsQA8Qa%2FsVGx3axO8ZNik0h%2FoJAJXcsg7v1KkK0LruDQeXC03Ly8G4hc4OqV04w%3D%3D',
        }



    def update_cookies_with_browser(self):
        """使用DrissionPage打开浏览器更新cookies"""
        try:
            self.logger.info("正在打开浏览器更新cookies...")

            # 创建浏览器页面
            page = ChromiumPage()

            # 访问网站
            page.get(self.base_url)

            # 等待用户手动登录或处理验证
            input("请在浏览器中完成登录，然后按回车键继续...")

            # 获取更新后的cookies
            browser_cookies = page.cookies()

            # 转换cookies格式并更新
            self.cookies = {}
            for cookie in browser_cookies:
                self.cookies[cookie['name']] = cookie['value']

            # 关闭浏览器
            page.quit()

            self.logger.info("成功更新cookies")
            return True

        except Exception as e:
            self.logger.error(f"更新cookies失败: {e}")
            return False

    def search(self,query,page,type,sort):
        type_list = ['searchCommunityContent','SearchBowlsCG']
        sort_list = ['RELEVANT','RECENT']
        url = 'https://www.glassdoor.com.hk/graph'
        json_data = {
            'operationName': type,
            'variables': {
                'query': query,
                'count': 10,
                'cursor': page-1,
                'sort': sort,
            },
            'query': 'query searchCommunityContent($query: String!, $cursor: String, $count: Int, $sort: CommunitySearchSort) {\n  searchCommunityContent(\n    query: {query: $query, sort: $sort}\n    first: $count\n    after: $cursor\n  ) {\n    pageInfo {\n      endCursor\n      startCursor\n      __typename\n    }\n    edges {\n      allowedMembershipType\n      comments {\n        commentsCount\n        companyMentions {\n          aliasId\n          aliasName\n          __typename\n        }\n        creationDate\n        feedHandleUrl\n        feedIconUrl\n        feedId\n        feedName\n        id\n        lastMessageDate\n        likes {\n          date\n          reactionType\n          userId\n          __typename\n        }\n        matches {\n          end\n          start\n          __typename\n        }\n        messageData {\n          imageHeight\n          imageUrl\n          imageWidth\n          linkMetadata {\n            description\n            domain\n            imageUrl\n            title\n            url\n            __typename\n          }\n          text\n          __typename\n        }\n        messageType\n        postId\n        reactionCounters {\n          funny\n          helpful\n          like\n          smart\n          uplifting\n          __typename\n        }\n        repliesCount\n        sign {\n          displayNumber\n          prefixEnum\n          profileImage\n          type\n          value\n          __typename\n        }\n        __typename\n      }\n      commentsCount\n      companyMentions {\n        aliasId\n        aliasName\n        __typename\n      }\n      creationDate\n      feedHandle\n      feedHandleUrl\n      feedIcon\n      feedIconUrl\n      feedId\n      feedName\n      handleUrl\n      id\n      likes {\n        date\n        reactionType\n        userId\n        __typename\n      }\n      matches {\n        end\n        start\n        __typename\n      }\n      messageData {\n        imageHeight\n        imageUrl\n        imageWidth\n        linkMetadata {\n          description\n          domain\n          imageUrl\n          title\n          url\n          __typename\n        }\n        text\n        video {\n          captionsEnabled\n          height\n          thumbnail {\n            height\n            url\n            width\n            __typename\n          }\n          token\n          videoId\n          width\n          __typename\n        }\n        __typename\n      }\n      messageType\n      reactionCounters {\n        funny\n        helpful\n        like\n        smart\n        uplifting\n        __typename\n      }\n      sign {\n        displayNumber\n        prefixEnum\n        profileImage\n        type\n        value\n        __typename\n      }\n      url\n      __typename\n    }\n    __typename\n  }\n}',
        }
        res = requests.post(url, json=json_data, headers=self.headers,cookies=self.cookies)

        # 检查是否返回403，如果是则更新cookies后重试
        if res.status_code == 403:
            self.logger.warning("请求返回403，正在更新cookies...")
            if self.update_cookies_with_browser():
                # cookies更新成功，重新请求
                res = requests.post(url, json=json_data, headers=self.headers,cookies=self.cookies)
            else:
                self.logger.error("更新cookies失败")

        return res.json()
    
    def get_comment(self,id):
        comment_list = []
        url = 'https://www.glassdoor.com.hk/graph'
        json_data = {
            'operationName': 'GetFishbowlPostCommentsCG',
            'variables': {
                'params': {
                    'idOrHandleUrl': id,
                },
                'query': {
                    'sort': 'POINTS',
                },
            },
            'query': 'query GetFishbowlPostCommentsCG($params: GetFishbowlPostCommentsParams!, $query: GetFishbowlPostCommentsQuery) {\n  getFishbowlPostCommentsCG(params: $params, query: $query) {\n    comments {\n      ...FishbowlCommentCG\n      __typename\n    }\n    cursor\n    __typename\n  }\n}\n\nfragment FishbowlCommentCG on Comment {\n  _id\n  userId\n  isPostAuthor\n  postId\n  commentId\n  sign {\n    icon\n    name\n    signAccent\n    signType\n    metadata {\n      firstName\n      lastName\n      handleUrl\n      profileDiscoverableToPublic\n      __typename\n    }\n    __typename\n  }\n  commentType\n  messageType\n  canEdit\n  canDelete\n  myReaction\n  reactionCounters {\n    like\n    helpful\n    smart\n    funny\n    uplifting\n    __typename\n  }\n  likesCount\n  repliesCount\n  date\n  originService\n  messageData {\n    ... on ImageMessageData {\n      text\n      linkMetadata {\n        url\n        domain\n        imageUrl\n        title\n        description\n        __typename\n      }\n      images {\n        width\n        height\n        url\n        __typename\n      }\n      __typename\n    }\n    ... on TextMessageData {\n      text\n      linkMetadata {\n        url\n        domain\n        imageUrl\n        title\n        description\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  repliesPreviews {\n    _id\n    userId\n    isPostAuthor\n    postId\n    commentId\n    sign {\n      icon\n      name\n      signAccent\n      signType\n      metadata {\n        firstName\n        lastName\n        handleUrl\n        profileDiscoverableToPublic\n        __typename\n      }\n      __typename\n    }\n    commentType\n    messageType\n    canEdit\n    canDelete\n    myReaction\n    reactionCounters {\n      like\n      helpful\n      smart\n      funny\n      uplifting\n      __typename\n    }\n    likesCount\n    repliesCount\n    date\n    originService\n    messageData {\n      ... on ImageMessageData {\n        text\n        linkMetadata {\n          url\n          domain\n          imageUrl\n          title\n          description\n          __typename\n        }\n        images {\n          width\n          height\n          url\n          __typename\n        }\n        __typename\n      }\n      ... on TextMessageData {\n        text\n        linkMetadata {\n          url\n          domain\n          imageUrl\n          title\n          description\n          __typename\n        }\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  __typename\n}',
        }
        res = requests.post(url, json=json_data, headers=self.headers,cookies=self.cookies)

        # 检查是否返回403，如果是则更新cookies后重试
        if res.status_code == 403:
            self.logger.warning("获取评论请求返回403，正在更新cookies...")
            if self.update_cookies_with_browser():
                # cookies更新成功，重新请求
                res = requests.post(url, json=json_data, headers=self.headers,cookies=self.cookies)
            else:
                self.logger.error("更新cookies失败")
                return []

        res_json = res.json()
        cursor = res_json['data']['getFishbowlPostCommentsCG']['cursor']
        comment_list.extend(res_json['data']['getFishbowlPostCommentsCG']['comments'])
        if cursor:
            while True:
                json_data['variables']['query']['cursor'] = cursor
                json_data['variables']['query']['count'] = 10
                res = requests.post(url, json=json_data, headers=self.headers,cookies=self.cookies)

                # 检查分页请求是否返回403
                if res.status_code == 403:
                    self.logger.warning("获取更多评论请求返回403，停止获取")
                    break

                res_json = res.json()
                cursor = res_json['data']['getFishbowlPostCommentsCG']['cursor']
                comment_list.extend(res_json['data']['getFishbowlPostCommentsCG']['comments'])
                if not cursor:
                    break
        return comment_list
    def get_two_comments(self,comment_id,num):
        comment_list = []
        url = 'https://www.glassdoor.com.hk/graph'
        json_data = {
            'operationName': 'GetCommentRepliesCG',
            'variables': {
                'params': {
                    'id': comment_id,
                },
                'query': {
                    'count': 10,
                },
            },
            'query': 'query GetCommentRepliesCG($params: GetFishbowlCommentRepliesParams!, $query: GetFishbowlCommentRepliesQuery) {\n  getFishbowlCommentRepliesCG(params: $params, query: $query) {\n    cursor\n    replies {\n      ...FishbowlCommentCG\n      __typename\n    }\n    __typename\n  }\n}\n\nfragment FishbowlCommentCG on Comment {\n  _id\n  userId\n  isPostAuthor\n  postId\n  commentId\n  sign {\n    icon\n    name\n    signAccent\n    signType\n    metadata {\n      firstName\n      lastName\n      handleUrl\n      profileDiscoverableToPublic\n      __typename\n    }\n    __typename\n  }\n  commentType\n  messageType\n  canEdit\n  canDelete\n  myReaction\n  reactionCounters {\n    like\n    helpful\n    smart\n    funny\n    uplifting\n    __typename\n  }\n  likesCount\n  repliesCount\n  date\n  originService\n  messageData {\n    ... on ImageMessageData {\n      text\n      linkMetadata {\n        url\n        domain\n        imageUrl\n        title\n        description\n        __typename\n      }\n      images {\n        width\n        height\n        url\n        __typename\n      }\n      __typename\n    }\n    ... on TextMessageData {\n      text\n      linkMetadata {\n        url\n        domain\n        imageUrl\n        title\n        description\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  repliesPreviews {\n    _id\n    userId\n    isPostAuthor\n    postId\n    commentId\n    sign {\n      icon\n      name\n      signAccent\n      signType\n      metadata {\n        firstName\n        lastName\n        handleUrl\n        profileDiscoverableToPublic\n        __typename\n      }\n      __typename\n    }\n    commentType\n    messageType\n    canEdit\n    canDelete\n    myReaction\n    reactionCounters {\n      like\n      helpful\n      smart\n      funny\n      uplifting\n      __typename\n    }\n    likesCount\n    repliesCount\n    date\n    originService\n    messageData {\n      ... on ImageMessageData {\n        text\n        linkMetadata {\n          url\n          domain\n          imageUrl\n          title\n          description\n          __typename\n        }\n        images {\n          width\n          height\n          url\n          __typename\n        }\n        __typename\n      }\n      ... on TextMessageData {\n        text\n        linkMetadata {\n          url\n          domain\n          imageUrl\n          title\n          description\n          __typename\n        }\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  __typename\n}',
        }
        res = requests.post(url, json=json_data, headers=self.headers,cookies=self.cookies)
        res_json = res.json()
        cursor = res_json['data']['getFishbowlCommentRepliesCG']['cursor']
        comment_list.extend(res_json['data']['getFishbowlCommentRepliesCG']['replies'])
        if cursor:
            while True:
                json_data['variables']['query']['cursor'] = cursor
                res = requests.post(url, json=json_data, headers=self.headers,cookies=self.cookies)
                res_json = res.json()
                cursor = res_json['data']['getFishbowlCommentRepliesCG']['cursor']
                comment_list.extend(res_json['data']['getFishbowlCommentRepliesCG']['replies'])
                if not cursor:
                    break
        return comment_list


def run():
    spider = Spider()
    search_result = spider.search("aaa",1,0,0)

if __name__ == '__main__':
    run()