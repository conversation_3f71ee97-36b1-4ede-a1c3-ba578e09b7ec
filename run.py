#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Glassdoor Spider
爬虫主程序
"""

import requests
import time
import json
from typing import Dict, List, Optional
from urllib.parse import urljoin, urlparse
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


class Spider:
    """
    Glassdoor 爬虫类
    """
    
    def __init__(self, base_url: str = "https://www.glassdoor.com"):
        """
        初始化爬虫
        
        Args:
            base_url: 基础URL
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 请求间隔（秒）
        self.delay = 1
        
        self.logger.info("Spider initialized successfully")
    
    def get_page(self, url: str, params: Optional[Dict] = None) -> Optional[requests.Response]:
        """
        获取页面内容
        
        Args:
            url: 目标URL
            params: 请求参数
            
        Returns:
            Response对象或None
        """
        try:
            # 添加延时避免被封
            time.sleep(self.delay)
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            self.logger.info(f"Successfully fetched: {url}")
            return response
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching {url}: {str(e)}")
            return None
    
    def parse_data(self, response: requests.Response) -> Dict:
        """
        解析页面数据
        
        Args:
            response: 响应对象
            
        Returns:
            解析后的数据字典
        """
        # 这里可以根据具体需求实现数据解析逻辑
        data = {
            'url': response.url,
            'status_code': response.status_code,
            'content_length': len(response.content),
            'timestamp': time.time()
        }
        
        self.logger.info(f"Parsed data from: {response.url}")
        return data
    
    def save_data(self, data: Dict, filename: str = None) -> bool:
        """
        保存数据到文件
        
        Args:
            data: 要保存的数据
            filename: 文件名
            
        Returns:
            保存是否成功
        """
        try:
            if filename is None:
                filename = f"data_{int(time.time())}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Data saved to: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving data: {str(e)}")
            return False
    
    def run(self, target_url: str) -> None:
        """
        运行爬虫
        
        Args:
            target_url: 目标URL
        """
        self.logger.info(f"Starting spider for: {target_url}")
        
        # 获取页面
        response = self.get_page(target_url)
        if response is None:
            self.logger.error("Failed to fetch page")
            return
        
        # 解析数据
        data = self.parse_data(response)
        
        # 保存数据
        self.save_data(data)
        
        self.logger.info("Spider run completed")


def main():
    """
    主函数
    """
    # 创建爬虫实例
    spider = Spider()
    
    # 示例URL（需要根据实际需求修改）
    target_url = "https://www.glassdoor.com/Reviews/index.htm"
    
    # 运行爬虫
    spider.run(target_url)


if __name__ == "__main__":
    main()
