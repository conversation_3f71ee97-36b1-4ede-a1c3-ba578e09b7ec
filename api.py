from fastapi import FastAPI, HTTPException
import uvicorn
from run import Spider

# 创建FastAPI应用
app = FastAPI(
    title="Glassdoor爬虫API",
    description="Glassdoor数据爬取API接口",
    version="1.0.0"
)

# 全局Spider实例
spider = Spider()

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "Glassdoor爬虫API",
        "version": "1.0.0",
        "endpoints": [
            "/search - 搜索内容",
            "/comment - 获取评论", 
            "/two-comments - 获取二级评论"
        ]
    }

@app.get("/search")
async def search_content(query: str, page: int = 1, type: int = 0, sort: int = 0):
    """
    搜索内容
    
    Args:
        query: 搜索关键词
        page: 页码，默认1
        type: 搜索类型，0: searchCommunityContent, 1: SearchBowlsCG
        sort: 排序方式，0: RELEVANT, 1: RECENT
        
    Returns:
        搜索结果
    """
    try:
        result = spider.search(query, page, type, sort)
        
        if result is None:
            raise HTTPException(status_code=500, detail="搜索失败")
        
        return {
            "success": True,
            "message": "搜索成功",
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索过程中出现错误: {str(e)}")

@app.get("/comment")
async def get_comment(id: str):
    """
    获取评论
    
    Args:
        id: 帖子ID
        
    Returns:
        评论列表
    """
    try:
        result = spider.get_comment(id)
        
        if result is None:
            raise HTTPException(status_code=500, detail="获取评论失败")
        
        return {
            "success": True,
            "message": f"成功获取{len(result)}条评论",
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取评论过程中出现错误: {str(e)}")

@app.get("/two-comments")
async def get_two_comments(comment_id: str):
    """
    获取二级评论
    
    Args:
        comment_id: 评论ID
        
    Returns:
        二级评论列表
    """
    try:
        result = spider.get_two_comments(comment_id)
        
        if result is None:
            raise HTTPException(status_code=500, detail="获取二级评论失败")
        
        return {
            "success": True,
            "message": f"成功获取{len(result)}条二级评论",
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取二级评论过程中出现错误: {str(e)}")

if __name__ == "__main__":
    print("启动Glassdoor爬虫API服务...")
    print("API文档地址: http://localhost:8000/docs")
    print("API地址: http://localhost:8000")
    
    uvicorn.run(
        "api:app", 
        host="0.0.0.0", 
        port=8000,
        reload=True,
        log_level="info"
    )
