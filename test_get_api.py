import requests

# API基础URL
BASE_URL = "http://localhost:8000"

def extract_post_ids(search_data):
    """从搜索结果中提取帖子ID"""
    post_ids = []
    try:
        if 'data' in search_data and 'searchCommunityContent' in search_data['data']:
            edges = search_data['data']['searchCommunityContent']['edges']
            for edge in edges:
                if 'id' in edge:
                    post_ids.append(edge['id'])
        print(f"提取到 {len(post_ids)} 个帖子ID")
        return post_ids
    except Exception as e:
        print(f"提取帖子ID失败: {e}")
        return []

def extract_comment_ids(comment_data):
    """从评论结果中提取评论ID"""
    comment_ids = []
    try:
        if isinstance(comment_data, list):
            for comment in comment_data:
                if '_id' in comment:
                    comment_ids.append(comment['_id'])
        print(f"提取到 {len(comment_ids)} 个评论ID")
        return comment_ids
    except Exception as e:
        print(f"提取评论ID失败: {e}")
        return []

def parse_comment_content(comment_data):
    """解析评论内容"""
    try:
        if isinstance(comment_data, list):
            print(f"\n--- 评论内容解析 (共{len(comment_data)}条) ---")
            for i, comment in enumerate(comment_data, 1):
                print(f"\n评论 {i}:")
                print(f"  ID: {comment.get('_id', 'N/A')}")

                # 解析用户信息
                sign = comment.get('sign', {})
                user_name = sign.get('name', '匿名用户')
                print(f"  用户: {user_name}")

                # 解析评论内容
                message_data = comment.get('messageData', {})
                text = message_data.get('text', '无文本内容')
                print(f"  内容: {text[:100]}{'...' if len(text) > 100 else ''}")

                # 解析时间
                date = comment.get('date', 'N/A')
                print(f"  时间: {date}")

                # 解析点赞数等
                reaction_counters = comment.get('reactionCounters', {})
                likes = reaction_counters.get('like', 0)
                print(f"  点赞: {likes}")

                # 解析回复数
                replies_count = comment.get('repliesCount', 0)
                print(f"  回复数: {replies_count}")

                print("-" * 50)
    except Exception as e:
        print(f"解析评论内容失败: {e}")

def parse_two_comments_content(two_comments_data):
    """解析二级评论内容"""
    try:
        if isinstance(two_comments_data, list):
            print(f"\n--- 二级评论内容解析 (共{len(two_comments_data)}条) ---")
            for i, comment in enumerate(two_comments_data, 1):
                print(f"\n二级评论 {i}:")
                print(f"  ID: {comment.get('_id', 'N/A')}")

                # 解析用户信息
                sign = comment.get('sign', {})
                user_name = sign.get('name', '匿名用户')
                print(f"  用户: {user_name}")

                # 解析评论内容
                message_data = comment.get('messageData', {})
                text = message_data.get('text', '无文本内容')
                print(f"  内容: {text[:100]}{'...' if len(text) > 100 else ''}")

                # 解析时间
                date = comment.get('date', 'N/A')
                print(f"  时间: {date}")

                # 解析点赞数等
                reaction_counters = comment.get('reactionCounters', {})
                likes = reaction_counters.get('like', 0)
                print(f"  点赞: {likes}")

                print("-" * 30)
    except Exception as e:
        print(f"解析二级评论内容失败: {e}")

def main():
    """主测试函数"""
    print("开始测试Glassdoor爬虫API...")

    # 1. 测试搜索
    print("\n=== 1. 测试搜索 ===")
    search_response = requests.get(f"{BASE_URL}/search", params={
        "query": "python",
        "page": 1,
        "type": 0,
        "sort": 0
    })
    print(f"搜索状态码: {search_response.status_code}")

    if search_response.status_code == 200:
        search_result = search_response.json()
        print(f"搜索成功: {search_result.get('success')}")

        # 2. 解析帖子ID并测试评论
        if search_result.get('success') and search_result.get('data'):
            post_ids = extract_post_ids(search_result['data'])

            if post_ids:
                # 使用第一个帖子ID测试评论
                first_post_id = post_ids[0]
                print(f"\n=== 2. 测试获取评论 (帖子ID: {first_post_id}) ===")

                comment_response = requests.get(f"{BASE_URL}/comment", params={"id": first_post_id})
                print(f"评论状态码: {comment_response.status_code}")

                if comment_response.status_code == 200:
                    comment_result = comment_response.json()
                    print(f"获取评论成功: {comment_result.get('success')}")
                    print(f"评论消息: {comment_result.get('message')}")

                    # 解析并显示评论内容
                    if comment_result.get('success') and comment_result.get('data'):
                        parse_comment_content(comment_result['data'])

                        # 3. 解析评论ID并测试二级评论
                        comment_ids = extract_comment_ids(comment_result['data'])

                        if comment_ids:
                            # 使用第一个评论ID测试二级评论
                            first_comment_id = comment_ids[0]
                            print(f"\n=== 3. 测试获取二级评论 (评论ID: {first_comment_id}) ===")

                            two_comments_response = requests.get(f"{BASE_URL}/two-comments", params={"comment_id": first_comment_id})
                            print(f"二级评论状态码: {two_comments_response.status_code}")

                            if two_comments_response.status_code == 200:
                                two_comments_result = two_comments_response.json()
                                print(f"获取二级评论成功: {two_comments_result.get('success')}")
                                print(f"二级评论消息: {two_comments_result.get('message')}")

                                # 解析并显示二级评论内容
                                if two_comments_result.get('success') and two_comments_result.get('data'):
                                    parse_two_comments_content(two_comments_result['data'])
                            else:
                                print("获取二级评论失败")
                        else:
                            print("没有找到评论ID，跳过二级评论测试")
                    else:
                        print("获取评论失败，跳过二级评论测试")
                else:
                    print("获取评论请求失败")
            else:
                print("没有找到帖子ID，跳过评论测试")
        else:
            print("搜索失败，跳过后续测试")
    else:
        print("搜索请求失败")

    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
