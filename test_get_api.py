import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000"

def test_search():
    """测试搜索接口"""
    print("=== 测试搜索接口 ===")
    url = f"{BASE_URL}/search"
    params = {
        "query": "software engineer",
        "page": 1,
        "type": 0,
        "sort": 0
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"成功: {result.get('success')}")
        print(f"消息: {result.get('message')}")
        if result.get('data'):
            print(f"数据类型: {type(result.get('data'))}")
            print(f"数据键: {list(result.get('data').keys()) if isinstance(result.get('data'), dict) else '非字典类型'}")
        print()
        return result
    except Exception as e:
        print(f"搜索测试失败: {e}")
        print()
        return None

def test_comment():
    """测试获取评论接口"""
    print("=== 测试获取评论接口 ===")
    url = f"{BASE_URL}/comment"
    params = {
        "id": "test_post_id"  # 这里需要一个真实的帖子ID
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"成功: {result.get('success')}")
        print(f"消息: {result.get('message')}")
        print()
        return result
    except Exception as e:
        print(f"获取评论测试失败: {e}")
        print()
        return None

def test_two_comments():
    """测试获取二级评论接口"""
    print("=== 测试获取二级评论接口 ===")
    url = f"{BASE_URL}/two-comments"
    params = {
        "comment_id": "test_comment_id",  # 这里需要一个真实的评论ID
        "num": 5
    }
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"成功: {result.get('success')}")
        print(f"消息: {result.get('message')}")
        print()
        return result
    except Exception as e:
        print(f"获取二级评论测试失败: {e}")
        print()
        return None

def main():
    """主测试函数"""
    print("开始测试Glassdoor爬虫GET API...")
    print()
    
    # 测试根路径
    print("=== 测试根路径 ===")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
    except Exception as e:
        print(f"根路径测试失败: {e}")
        print()
    
    # 测试三个主要接口
    test_search()
    test_comment()
    test_two_comments()
    
    print("API测试完成!")
    print()
    print("API接口说明:")
    print("1. GET /search?query=关键词&page=页码&type=类型&sort=排序")
    print("2. GET /comment?id=帖子ID")
    print("3. GET /two-comments?comment_id=评论ID&num=数量")

if __name__ == "__main__":
    main()
